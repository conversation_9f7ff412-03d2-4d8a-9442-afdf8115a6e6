import { defineOverridesPreferences } from '@vben/preferences';

/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 * !!! 更改配置后请清空缓存，否则可能不生效
 */
export const overridesPreferences = defineOverridesPreferences({
  // overrides
  app: {
    name: import.meta.env.VITE_APP_TITLE,
    layout: 'sidebar-mixed-nav', // 设置默认布局为双列菜单
  },
  theme: {
    mode: 'light', // 设置默认主题为浅色主题
    semiDarkSidebar: true, // 启用深色侧边栏
    semiDarkHeader: true, // 启用深色顶栏
  },
  widget: {
    globalSearch: false, // 隐藏全局搜索功能
    languageToggle: false, // 隐藏语言切换功能
  },
});
