<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, h, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationCodeLogin, z } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useAccessStore, useUserStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { codeLoginApi, sendCodeApi } from '#/api/core/auth';
import { getUserInfoApi } from '#/api/core/user';

defineOptions({ name: 'CodeLogin' });

const router = useRouter();
const accessStore = useAccessStore();
const userStore = useUserStore();
const loading = ref(false);
const sendCodeLoading = ref(false);
const CODE_LENGTH = 6;

// 组件引用
const codeLoginRef = ref<any>(null);

// 获取表单API
const getFormApi = () => {
  if (codeLoginRef.value) {
    return codeLoginRef.value.getFormApi();
  }
  return null;
};

/**
 * 发送验证码
 */
async function handleSendCode() {
  try {
    const formApi = getFormApi();
    if (!formApi) {
      message.error('表单未初始化');
      return;
    }

    // 获取表单数据
    const values = await formApi.getValues();
    const phoneNumber = values.phoneNumber;

    if (!phoneNumber) {
      message.error($t('authentication.mobileTip'));
      return;
    }

    // 验证手机号格式
    if (!/^\d{11}$/.test(phoneNumber)) {
      message.error($t('authentication.mobileErrortip'));
      return;
    }

    sendCodeLoading.value = true;

    // 调用真实的发送验证码API
    await sendCodeApi({ tell: phoneNumber });

    message.success(
      `验证码已发送至 ${phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}`,
    );
  } catch (error: any) {
    console.error('发送验证码失败:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      '发送验证码失败，请稍后重试';
    message.error(errorMessage);
  } finally {
    sendCodeLoading.value = false;
  }
}

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.mobile'),
      },
      fieldName: 'phoneNumber',
      label: $t('authentication.mobile'),
      rules: z
        .string()
        .min(1, { message: $t('authentication.mobileTip') })
        .refine((v) => /^\d{11}$/.test(v), {
          message: $t('authentication.mobileErrortip'),
        }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.code'),
        type: 'number',
        maxlength: CODE_LENGTH,
      },
      fieldName: 'code',
      label: $t('authentication.code'),
      rules: z
        .string()
        .min(1, { message: $t('authentication.codeTip', [CODE_LENGTH]) })
        .refine((v) => /^\d{6}$/.test(v), {
          message: $t('authentication.codeTip', [CODE_LENGTH]),
        }),
      renderComponentContent: ({ field }) => {
        return h('div', { class: 'flex gap-2' }, [
          h('input', {
            ...field,
            type: 'number',
            placeholder: $t('authentication.code'),
            maxlength: CODE_LENGTH,
            class: 'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 flex-1 rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
          }),
          h('button', {
            type: 'button',
            class: 'px-4 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap',
            disabled: sendCodeLoading.value,
            onClick: handleSendCode,
          }, sendCodeLoading.value ? '发送中...' : $t('authentication.sendCode'))
        ]);
      },
    },
  ];
});
/**
 * 异步处理登录操作
 * Asynchronously handle the login process
 * @param values 登录表单数据
 */
async function handleLogin(values: Recordable<any>) {
  try {
    loading.value = true;

    const { phoneNumber, code } = values;

    if (!phoneNumber || !code) {
      message.error('请输入手机号和验证码');
      return;
    }

    // 验证手机号格式
    if (!/^\d{11}$/.test(phoneNumber)) {
      message.error($t('authentication.mobileErrortip'));
      return;
    }

    // 验证验证码格式
    if (code.length !== CODE_LENGTH) {
      message.error($t('authentication.codeTip', [CODE_LENGTH]));
      return;
    }

    // 调用真实的验证码登录API
    const response = await codeLoginApi({ tell: phoneNumber, code });

    message.success('登录成功');

    // 登录成功后处理
    if (response) {
      // 设置访问令牌
      accessStore.setAccessToken(response);

      // 获取真实的用户信息（包括uid字段）
      try {
        const userInfo = await getUserInfoApi();
        userStore.setUserInfo(userInfo);
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 如果获取用户信息失败，设置基本的用户信息作为后备
        const fallbackUserInfo = {
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=default',
          realName: '用户',
          roles: ['user'],
          userId: '1',
          username: phoneNumber, // 使用手机号作为用户名
        };
        userStore.setUserInfo(fallbackUserInfo);
        message.warning('获取用户详细信息失败，使用默认信息');
      }
    }

    // 登录成功后跳转到默认页面
    // 使用Vue Router进行导航，保持单页应用状态
    setTimeout(() => {
      router.push('/');
    }, 1000);
  } catch (error: any) {
    console.error('登录失败:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      '登录失败，请稍后重试';
    message.error(errorMessage);
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <AuthenticationCodeLogin
    ref="codeLoginRef"
    :form-schema="formSchema"
    :loading="loading"
    qr-code-login-path="/auth/qrcode-login"
    @submit="handleLogin"
  />
</template>
